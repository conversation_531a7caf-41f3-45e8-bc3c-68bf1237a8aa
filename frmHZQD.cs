﻿using HIH.Framework.Common.Data;
using System;
using System.Collections;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace HIH.CMS.Main
{
    public partial class frmHZQD : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        string strSql = "";
        string strWhere = "";
        string strOrderBy = "";
        DataTable dt = new DataTable();
        public frmHZQD()
        {
            InitializeComponent();
            //isLoadPerm = false;
            strSql = @"select 
a.上级ID,
b.核注清单编号,
a.商品序号,
a.备案序号,
a.商品料号,
a.商品编码,
a.商品名称,
a.单价,
a.总价,
a.币制,
a.申报计量单位,
a.申报数量,
a.剩余申报数量,
a.已核销申报数量,
a.法定计量单位,
a.法定数量,
a.剩余法定数量,
a.已核销法定数量,
a.第二法定计量单位,
a.第二法定数量,
a.剩余第二法定数量,
a.已核销第二法定数量,
a.原产国
 from 进口核注清单明细表 a 
 inner join 进口核注清单表 b ON a.上级ID = b.ID  where 1 = 1 ";

        }

        private void frmHZQD_Load(object sender, EventArgs e)
        {
            LoadData();
        }

        public void LoadData()
        {
            strWhere = "";
            if (!string.IsNullOrEmpty(txtHZQDBH.Text))
            {
                strWhere += " and b.核注清单编号 like '%" + txtHZQDBH.Text + "%'";
            }
            if (!string.IsNullOrEmpty(txtSPLH.Text))
            {
                strWhere += " and a.商品料号 like '%" + txtSPLH.Text + "%'";
            }
            if (!string.IsNullOrEmpty(txtBAXH.Text))
            {
                strWhere += " and a.备案序号 = '" + txtBAXH.Text + "'";
            }
            if (!string.IsNullOrEmpty(txtSPXH.Text))
            {
                strWhere += " and a.商品序号 = '" + txtSPXH.Text + "'";
            }
            string SQL = strSql + strWhere + strOrderBy;
            dt = SqlHelper.FillDataTable(SQL);
            bs.DataSource = dt;
            gd.DataSource = bs;
            gdv.BestFitColumns();
            gdv.Columns[0].Visible = false;

        }
        #region 按钮操作
        private void btnClear_Click(object sender, EventArgs e)
        {
            txtHZQDBH.Text = "";
            txtSPLH.Text = "";
            txtBAXH.Text = "";
            txtSPXH.Text = "";
            LoadData();

        }

        private void btnJKDR_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            ICF.ISD.SubFormShowModal("HIH.CMS.Main.dll",
                                        "HIH.CMS.Main.frmPDFJK",
                                        "进口核注识别",
                                         null);
        }

        private void btnCKDR_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            ICF.ISD.SubFormShowModal("HIH.CMS.Main.dll",
                                       "HIH.CMS.Main.frmPDFCK",
                                       "出口核注识别",
                                        null);
        }

        #endregion

        private void btnReturn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DoReturn();
        }

        private void btnKSCX_Click(object sender, EventArgs e)
        {
            LoadData();
        }


        private void btnCKMX_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            string baxh = gdv.GetRowCellValue(gdv.FocusedRowHandle, "备案序号").ToString();
            if (string.IsNullOrEmpty(baxh))
            {
                baxh = "-1";
            }
            ICF.ISD.SubFormShowModal("HIH.CMS.Main.dll",
                                        "HIH.CMS.Main.frmCKDetail",
                                        "出口核注明细",
                                         new ArrayList() { baxh });
        }

        private void btnJKMX_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            string sjID = gdv.GetRowCellValue(gdv.FocusedRowHandle, "上级ID").ToString();
            //双击跳入明细页面
            ICF.ISD.SubFormShowModal("HIH.CMS.Main.dll",
                                        "HIH.CMS.Main.frmPDFJK",
                                        "核注清单明细",
                                         new ArrayList() { sjID });
        }

        private void gdv_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hInfo = gdv.CalcHitInfo(new Point(e.X, e.Y));
                if (e.Button == MouseButtons.Left && e.Clicks == 2)//判断是否左键双击
                {
                    //判断光标是否在行范围内
                    if (hInfo.InRow)
                    {
                        string sjID = gdv.GetRowCellValue(gdv.FocusedRowHandle, "上级ID").ToString();
                        //双击跳入明细页面
                        ICF.ISD.SubFormShowModal("HIH.CMS.Main.dll",
                                                    "HIH.CMS.Main.frmPDFJK",
                                                    "进口核注清单明细",
                                                     new ArrayList() { sjID });
                    }
                }
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 导出Excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExcel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            string FileName = "";
            try
            {
                if (!ICF.ISD.SaveDialog("导出EXCEL", "XLSX文件(*.xlsx)|*.xlsx", out FileName))
                    return;
                gdv.ExportToXlsx(FileName);

            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

    }
}
