﻿
namespace HIH.CMS.Main
{
    partial class frmPDFCK
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmPDFCK));
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl1 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl2 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl3 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl4 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl5 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl6 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl7 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl8 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl9 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl referenceControl10 = new HIH.Framework.BaseUIDX.BaseCRMForm.ReferenceControl();
            this.txtHZQDBH = new DevExpress.XtraEditors.TextEdit();
            this.bar = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.btnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.btnSelectFile = new DevExpress.XtraBars.BarButtonItem();
            this.ckbEdit = new DevExpress.XtraBars.BarCheckItem();
            this.btnHD = new DevExpress.XtraBars.BarButtonItem();
            this.btnDel = new DevExpress.XtraBars.BarButtonItem();
            this.btnClear = new DevExpress.XtraBars.BarButtonItem();
            this.btnReturn = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.btnAdd = new DevExpress.XtraBars.BarButtonItem();
            this.btnJS = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.lblRemark = new System.Windows.Forms.Label();
            this.gd = new DevExpress.XtraGrid.GridControl();
            this.gdv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.商品序号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.备案序号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.商品料号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.商品编码 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.商品名称 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.申报计量单位 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.申报数量 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.单价 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.总价 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.币制 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.法定计量单位 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.法定数量 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.第二法定计量单位 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.第二法定数量 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.原产国 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.进口核注ID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.txtHL = new DevExpress.XtraEditors.TextEdit();
            this.txtID = new DevExpress.XtraEditors.TextEdit();
            this.txtTime = new DevExpress.XtraEditors.TextEdit();
            this.txtGLBGDBH = new DevExpress.XtraEditors.TextEdit();
            this.txtCZSJ = new DevExpress.XtraEditors.TextEdit();
            this.txtCZR = new DevExpress.XtraEditors.TextEdit();
            this.txtBGDBH = new DevExpress.XtraEditors.TextEdit();
            this.txtQDLX = new DevExpress.XtraEditors.TextEdit();
            this.txtZSHKSJ = new DevExpress.XtraEditors.DateEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lblHZQDBH = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.lblBGDBH = new DevExpress.XtraLayout.LayoutControlItem();
            this.lblGLBGDBH = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem4 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem6 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.lblTime = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem7 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.lblID = new DevExpress.XtraLayout.LayoutControlItem();
            this.lblQDLX = new DevExpress.XtraLayout.LayoutControlItem();
            this.lblCZR = new DevExpress.XtraLayout.LayoutControlItem();
            this.lblCZSJ = new DevExpress.XtraLayout.LayoutControlItem();
            this.lblZSHKSJ = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem8 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.lblHL = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem5 = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.lblZT = new DevExpress.XtraEditors.LabelControl();
            this.timer = new System.Windows.Forms.Timer();
            ((System.ComponentModel.ISupportInitialize)(this.bs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtHZQDBH.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bar)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gd)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtHL.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtID.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTime.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGLBGDBH.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCZSJ.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCZR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBGDBH.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtQDLX.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtZSHKSJ.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtZSHKSJ.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblHZQDBH)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblBGDBH)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblGLBGDBH)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblQDLX)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblCZR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblCZSJ)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblZSHKSJ)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblHL)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1.Panel1)).BeginInit();
            this.splitContainerControl1.Panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1.Panel2)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // txtHZQDBH
            // 
            this.txtHZQDBH.Location = new System.Drawing.Point(477, 12);
            this.txtHZQDBH.MenuManager = this.bar;
            this.txtHZQDBH.Name = "txtHZQDBH";
            this.txtHZQDBH.Size = new System.Drawing.Size(249, 20);
            this.txtHZQDBH.StyleController = this.layoutControl1;
            this.txtHZQDBH.TabIndex = 4;
            // 
            // bar
            // 
            this.bar.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.bar.DockControls.Add(this.barDockControlTop);
            this.bar.DockControls.Add(this.barDockControlBottom);
            this.bar.DockControls.Add(this.barDockControlLeft);
            this.bar.DockControls.Add(this.barDockControlRight);
            this.bar.Form = this;
            this.bar.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnReturn,
            this.btnAdd,
            this.btnClear,
            this.btnJS,
            this.btnSelectFile,
            this.btnRefresh,
            this.barButtonItem1,
            this.btnHD,
            this.btnDel,
            this.ckbEdit});
            this.bar.MaxItemId = 56;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnSelectFile, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.ckbEdit),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnHD),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnDel),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnClear, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnReturn)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableClose = true;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.Text = "Tools";
            // 
            // btnRefresh
            // 
            this.btnRefresh.Caption = "刷新";
            this.btnRefresh.Id = 46;
            this.btnRefresh.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnRefresh.ImageOptions.Image")));
            this.btnRefresh.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnRefresh.ImageOptions.LargeImage")));
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRefresh_ItemClick);
            // 
            // btnSelectFile
            // 
            this.btnSelectFile.Caption = "选择文件";
            this.btnSelectFile.Id = 45;
            this.btnSelectFile.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSelectFile.ImageOptions.Image")));
            this.btnSelectFile.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnSelectFile.ImageOptions.LargeImage")));
            this.btnSelectFile.Name = "btnSelectFile";
            this.btnSelectFile.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnSelectFile_ItemClick);
            // 
            // ckbEdit
            // 
            this.ckbEdit.Caption = "编辑";
            this.ckbEdit.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.ckbEdit.Id = 54;
            this.ckbEdit.Name = "ckbEdit";
            this.ckbEdit.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.ckbEdit_CheckedChanged);
            // 
            // btnHD
            // 
            this.btnHD.Caption = "核销";
            this.btnHD.Id = 49;
            this.btnHD.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnHD.ImageOptions.Image")));
            this.btnHD.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnHD.ImageOptions.LargeImage")));
            this.btnHD.Name = "btnHD";
            this.btnHD.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnHD.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnHD_ItemClick);
            // 
            // btnDel
            // 
            this.btnDel.Caption = "取消核销";
            this.btnDel.Id = 50;
            this.btnDel.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnDel.ImageOptions.Image")));
            this.btnDel.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnDel.ImageOptions.LargeImage")));
            this.btnDel.Name = "btnDel";
            this.btnDel.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnDel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnDel_ItemClick);
            // 
            // btnClear
            // 
            this.btnClear.Caption = "清空";
            this.btnClear.Id = 43;
            this.btnClear.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnClear.ImageOptions.SvgImage")));
            this.btnClear.Name = "btnClear";
            this.btnClear.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnClear_ItemClick);
            // 
            // btnReturn
            // 
            this.btnReturn.Caption = "返回";
            this.btnReturn.Id = 6;
            this.btnReturn.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnReturn.ImageOptions.Image")));
            this.btnReturn.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnReturn.ImageOptions.LargeImage")));
            this.btnReturn.Name = "btnReturn";
            this.btnReturn.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnReturn.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnReturn_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.bar;
            this.barDockControlTop.Size = new System.Drawing.Size(1372, 24);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 673);
            this.barDockControlBottom.Manager = this.bar;
            this.barDockControlBottom.Size = new System.Drawing.Size(1372, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 24);
            this.barDockControlLeft.Manager = this.bar;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 649);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1372, 24);
            this.barDockControlRight.Manager = this.bar;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 649);
            // 
            // btnAdd
            // 
            this.btnAdd.Caption = "保存";
            this.btnAdd.Id = 17;
            this.btnAdd.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnAdd.ImageOptions.SvgImage")));
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnAdd.Tag = "";
            // 
            // btnJS
            // 
            this.btnJS.Caption = "结束";
            this.btnJS.Id = 44;
            this.btnJS.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnJS.ImageOptions.SvgImage")));
            this.btnJS.Name = "btnJS";
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Caption = "识别导入";
            this.barButtonItem1.Id = 47;
            this.barButtonItem1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem1.ImageOptions.Image")));
            this.barButtonItem1.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem1.ImageOptions.LargeImage")));
            this.barButtonItem1.Name = "barButtonItem1";
            this.barButtonItem1.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // layoutControl1
            // 
            this.layoutControl1.Controls.Add(this.lblRemark);
            this.layoutControl1.Controls.Add(this.gd);
            this.layoutControl1.Controls.Add(this.txtHL);
            this.layoutControl1.Controls.Add(this.txtID);
            this.layoutControl1.Controls.Add(this.txtTime);
            this.layoutControl1.Controls.Add(this.txtGLBGDBH);
            this.layoutControl1.Controls.Add(this.txtCZSJ);
            this.layoutControl1.Controls.Add(this.txtCZR);
            this.layoutControl1.Controls.Add(this.txtBGDBH);
            this.layoutControl1.Controls.Add(this.txtQDLX);
            this.layoutControl1.Controls.Add(this.txtHZQDBH);
            this.layoutControl1.Controls.Add(this.txtZSHKSJ);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(0, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(771, 0, 650, 400);
            this.layoutControl1.Root = this.Root;
            this.layoutControl1.Size = new System.Drawing.Size(1372, 649);
            this.layoutControl1.TabIndex = 8;
            this.layoutControl1.Text = "layoutControl1";
            // 
            // lblRemark
            // 
            this.lblRemark.Font = new System.Drawing.Font("Tahoma", 8F);
            this.lblRemark.ForeColor = System.Drawing.Color.Red;
            this.lblRemark.Location = new System.Drawing.Point(953, 60);
            this.lblRemark.Name = "lblRemark";
            this.lblRemark.Size = new System.Drawing.Size(300, 20);
            this.lblRemark.TabIndex = 14;
            this.lblRemark.TextAlign = System.Drawing.ContentAlignment.BottomLeft;
            // 
            // gd
            // 
            this.gd.Location = new System.Drawing.Point(12, 108);
            this.gd.MainView = this.gdv;
            this.gd.MenuManager = this.bar;
            this.gd.Name = "gd";
            this.gd.Size = new System.Drawing.Size(1348, 529);
            this.gd.TabIndex = 7;
            this.gd.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gdv});
            // 
            // gdv
            // 
            this.gdv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.商品序号,
            this.备案序号,
            this.商品料号,
            this.商品编码,
            this.商品名称,
            this.申报计量单位,
            this.申报数量,
            this.单价,
            this.总价,
            this.币制,
            this.法定计量单位,
            this.法定数量,
            this.第二法定计量单位,
            this.第二法定数量,
            this.原产国,
            this.进口核注ID});
            this.gdv.GridControl = this.gd;
            this.gdv.Name = "gdv";
            this.gdv.OptionsBehavior.Editable = false;
            this.gdv.OptionsView.ColumnAutoWidth = false;
            this.gdv.OptionsView.ShowGroupPanel = false;
            this.gdv.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gdv_KeyDown);
            // 
            // 商品序号
            // 
            this.商品序号.Caption = "商品序号";
            this.商品序号.FieldName = "商品序号";
            this.商品序号.Name = "商品序号";
            this.商品序号.Visible = true;
            this.商品序号.VisibleIndex = 0;
            this.商品序号.Width = 58;
            // 
            // 备案序号
            // 
            this.备案序号.Caption = "备案序号";
            this.备案序号.FieldName = "备案序号";
            this.备案序号.Name = "备案序号";
            this.备案序号.Visible = true;
            this.备案序号.VisibleIndex = 1;
            this.备案序号.Width = 56;
            // 
            // 商品料号
            // 
            this.商品料号.Caption = "商品料号";
            this.商品料号.FieldName = "商品料号";
            this.商品料号.Name = "商品料号";
            this.商品料号.Visible = true;
            this.商品料号.VisibleIndex = 2;
            this.商品料号.Width = 76;
            // 
            // 商品编码
            // 
            this.商品编码.Caption = "商品编码";
            this.商品编码.FieldName = "商品编码";
            this.商品编码.Name = "商品编码";
            this.商品编码.Visible = true;
            this.商品编码.VisibleIndex = 3;
            this.商品编码.Width = 74;
            // 
            // 商品名称
            // 
            this.商品名称.Caption = "商品名称";
            this.商品名称.FieldName = "商品名称";
            this.商品名称.Name = "商品名称";
            this.商品名称.Visible = true;
            this.商品名称.VisibleIndex = 4;
            this.商品名称.Width = 74;
            // 
            // 申报计量单位
            // 
            this.申报计量单位.Caption = "申报计量单位";
            this.申报计量单位.FieldName = "申报计量单位";
            this.申报计量单位.Name = "申报计量单位";
            this.申报计量单位.Visible = true;
            this.申报计量单位.VisibleIndex = 5;
            this.申报计量单位.Width = 89;
            // 
            // 申报数量
            // 
            this.申报数量.Caption = "申报数量";
            this.申报数量.FieldName = "申报数量";
            this.申报数量.Name = "申报数量";
            this.申报数量.Visible = true;
            this.申报数量.VisibleIndex = 6;
            this.申报数量.Width = 72;
            // 
            // 单价
            // 
            this.单价.Caption = "单价";
            this.单价.FieldName = "单价";
            this.单价.Name = "单价";
            this.单价.Visible = true;
            this.单价.VisibleIndex = 7;
            this.单价.Width = 50;
            // 
            // 总价
            // 
            this.总价.Caption = "总价";
            this.总价.FieldName = "总价";
            this.总价.Name = "总价";
            this.总价.Visible = true;
            this.总价.VisibleIndex = 8;
            this.总价.Width = 66;
            // 
            // 币制
            // 
            this.币制.Caption = "币制";
            this.币制.FieldName = "币制";
            this.币制.Name = "币制";
            this.币制.Visible = true;
            this.币制.VisibleIndex = 9;
            this.币制.Width = 45;
            // 
            // 法定计量单位
            // 
            this.法定计量单位.Caption = "法定计量单位";
            this.法定计量单位.FieldName = "法定计量单位";
            this.法定计量单位.Name = "法定计量单位";
            this.法定计量单位.Visible = true;
            this.法定计量单位.VisibleIndex = 10;
            this.法定计量单位.Width = 77;
            // 
            // 法定数量
            // 
            this.法定数量.Caption = "法定数量";
            this.法定数量.FieldName = "法定数量";
            this.法定数量.Name = "法定数量";
            this.法定数量.Visible = true;
            this.法定数量.VisibleIndex = 11;
            this.法定数量.Width = 77;
            // 
            // 第二法定计量单位
            // 
            this.第二法定计量单位.Caption = "第二法定计量单位";
            this.第二法定计量单位.FieldName = "第二法定计量单位";
            this.第二法定计量单位.Name = "第二法定计量单位";
            this.第二法定计量单位.Visible = true;
            this.第二法定计量单位.VisibleIndex = 12;
            this.第二法定计量单位.Width = 95;
            // 
            // 第二法定数量
            // 
            this.第二法定数量.Caption = "第二法定数量";
            this.第二法定数量.FieldName = "第二法定数量";
            this.第二法定数量.Name = "第二法定数量";
            this.第二法定数量.Visible = true;
            this.第二法定数量.VisibleIndex = 13;
            this.第二法定数量.Width = 87;
            // 
            // 原产国
            // 
            this.原产国.Caption = "原产国";
            this.原产国.FieldName = "原产国";
            this.原产国.Name = "原产国";
            this.原产国.Visible = true;
            this.原产国.VisibleIndex = 14;
            this.原产国.Width = 62;
            // 
            // 进口核注ID
            // 
            this.进口核注ID.Caption = "进口核注ID";
            this.进口核注ID.FieldName = "进口核注ID";
            this.进口核注ID.Name = "进口核注ID";
            // 
            // txtHL
            // 
            this.txtHL.Enabled = false;
            this.txtHL.Location = new System.Drawing.Point(826, 60);
            this.txtHL.MenuManager = this.bar;
            this.txtHL.Name = "txtHL";
            this.txtHL.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.txtHL.Properties.MaskSettings.Set("MaskManagerSignature", "allowNull=False");
            this.txtHL.Size = new System.Drawing.Size(123, 20);
            this.txtHL.StyleController = this.layoutControl1;
            this.txtHL.TabIndex = 13;
            this.txtHL.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtHL_EditValueChanging);
            // 
            // txtID
            // 
            this.txtID.Location = new System.Drawing.Point(108, 12);
            this.txtID.MenuManager = this.bar;
            this.txtID.Name = "txtID";
            this.txtID.Properties.ReadOnly = true;
            this.txtID.Size = new System.Drawing.Size(269, 20);
            this.txtID.StyleController = this.layoutControl1;
            this.txtID.TabIndex = 12;
            // 
            // txtTime
            // 
            this.txtTime.Location = new System.Drawing.Point(826, 84);
            this.txtTime.MenuManager = this.bar;
            this.txtTime.Name = "txtTime";
            this.txtTime.Properties.ReadOnly = true;
            this.txtTime.Size = new System.Drawing.Size(123, 20);
            this.txtTime.StyleController = this.layoutControl1;
            this.txtTime.TabIndex = 11;
            // 
            // txtGLBGDBH
            // 
            this.txtGLBGDBH.Location = new System.Drawing.Point(108, 60);
            this.txtGLBGDBH.MenuManager = this.bar;
            this.txtGLBGDBH.Name = "txtGLBGDBH";
            this.txtGLBGDBH.Size = new System.Drawing.Size(269, 20);
            this.txtGLBGDBH.StyleController = this.layoutControl1;
            this.txtGLBGDBH.TabIndex = 10;
            // 
            // txtCZSJ
            // 
            this.txtCZSJ.Location = new System.Drawing.Point(477, 84);
            this.txtCZSJ.MenuManager = this.bar;
            this.txtCZSJ.Name = "txtCZSJ";
            this.txtCZSJ.Properties.ReadOnly = true;
            this.txtCZSJ.Size = new System.Drawing.Size(249, 20);
            this.txtCZSJ.StyleController = this.layoutControl1;
            this.txtCZSJ.TabIndex = 9;
            // 
            // txtCZR
            // 
            this.txtCZR.Location = new System.Drawing.Point(108, 84);
            this.txtCZR.MenuManager = this.bar;
            this.txtCZR.Name = "txtCZR";
            this.txtCZR.Properties.ReadOnly = true;
            this.txtCZR.Size = new System.Drawing.Size(269, 20);
            this.txtCZR.StyleController = this.layoutControl1;
            this.txtCZR.TabIndex = 8;
            // 
            // txtBGDBH
            // 
            this.txtBGDBH.Location = new System.Drawing.Point(477, 36);
            this.txtBGDBH.MenuManager = this.bar;
            this.txtBGDBH.Name = "txtBGDBH";
            this.txtBGDBH.Size = new System.Drawing.Size(249, 20);
            this.txtBGDBH.StyleController = this.layoutControl1;
            this.txtBGDBH.TabIndex = 7;
            // 
            // txtQDLX
            // 
            this.txtQDLX.Location = new System.Drawing.Point(108, 36);
            this.txtQDLX.MenuManager = this.bar;
            this.txtQDLX.Name = "txtQDLX";
            this.txtQDLX.Size = new System.Drawing.Size(269, 20);
            this.txtQDLX.StyleController = this.layoutControl1;
            this.txtQDLX.TabIndex = 5;
            // 
            // txtZSHKSJ
            // 
            this.txtZSHKSJ.EditValue = null;
            this.txtZSHKSJ.Location = new System.Drawing.Point(477, 60);
            this.txtZSHKSJ.MenuManager = this.bar;
            this.txtZSHKSJ.Name = "txtZSHKSJ";
            this.txtZSHKSJ.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.txtZSHKSJ.Properties.CalendarTimeEditing = DevExpress.Utils.DefaultBoolean.True;
            this.txtZSHKSJ.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.txtZSHKSJ.Properties.DisplayFormat.FormatString = "";
            this.txtZSHKSJ.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.txtZSHKSJ.Properties.EditFormat.FormatString = "";
            this.txtZSHKSJ.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.txtZSHKSJ.Properties.MaskSettings.Set("mask", "yyyy/MM/dd HH:mm:ss");
            this.txtZSHKSJ.Size = new System.Drawing.Size(152, 20);
            this.txtZSHKSJ.StyleController = this.layoutControl1;
            this.txtZSHKSJ.TabIndex = 6;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lblHZQDBH,
            this.emptySpaceItem3,
            this.lblBGDBH,
            this.lblGLBGDBH,
            this.emptySpaceItem4,
            this.emptySpaceItem6,
            this.lblTime,
            this.emptySpaceItem7,
            this.lblID,
            this.lblQDLX,
            this.lblCZR,
            this.lblCZSJ,
            this.lblZSHKSJ,
            this.emptySpaceItem8,
            this.lblHL,
            this.layoutControlItem1,
            this.layoutControlItem4});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(1372, 649);
            this.Root.TextVisible = false;
            // 
            // lblHZQDBH
            // 
            this.lblHZQDBH.Control = this.txtHZQDBH;
            this.lblHZQDBH.Location = new System.Drawing.Point(369, 0);
            this.lblHZQDBH.Name = "lblHZQDBH";
            this.lblHZQDBH.Size = new System.Drawing.Size(349, 24);
            this.lblHZQDBH.Text = "核注清单编号";
            this.lblHZQDBH.TextSize = new System.Drawing.Size(84, 14);
            // 
            // emptySpaceItem3
            // 
            this.emptySpaceItem3.AllowHotTrack = false;
            this.emptySpaceItem3.Location = new System.Drawing.Point(941, 0);
            this.emptySpaceItem3.Name = "emptySpaceItem3";
            this.emptySpaceItem3.Size = new System.Drawing.Size(304, 48);
            this.emptySpaceItem3.TextSize = new System.Drawing.Size(0, 0);
            // 
            // lblBGDBH
            // 
            this.lblBGDBH.Control = this.txtBGDBH;
            this.lblBGDBH.Location = new System.Drawing.Point(369, 24);
            this.lblBGDBH.Name = "lblBGDBH";
            this.lblBGDBH.Size = new System.Drawing.Size(349, 24);
            this.lblBGDBH.Text = "报关单编号";
            this.lblBGDBH.TextSize = new System.Drawing.Size(84, 14);
            // 
            // lblGLBGDBH
            // 
            this.lblGLBGDBH.Control = this.txtGLBGDBH;
            this.lblGLBGDBH.Location = new System.Drawing.Point(0, 48);
            this.lblGLBGDBH.Name = "lblGLBGDBH";
            this.lblGLBGDBH.Size = new System.Drawing.Size(369, 24);
            this.lblGLBGDBH.Text = "关联报关单编号";
            this.lblGLBGDBH.TextSize = new System.Drawing.Size(84, 14);
            // 
            // emptySpaceItem4
            // 
            this.emptySpaceItem4.AllowHotTrack = false;
            this.emptySpaceItem4.Location = new System.Drawing.Point(1245, 0);
            this.emptySpaceItem4.Name = "emptySpaceItem4";
            this.emptySpaceItem4.Size = new System.Drawing.Size(107, 96);
            this.emptySpaceItem4.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem6
            // 
            this.emptySpaceItem6.AllowHotTrack = false;
            this.emptySpaceItem6.Location = new System.Drawing.Point(941, 72);
            this.emptySpaceItem6.Name = "emptySpaceItem6";
            this.emptySpaceItem6.Size = new System.Drawing.Size(304, 24);
            this.emptySpaceItem6.TextSize = new System.Drawing.Size(0, 0);
            // 
            // lblTime
            // 
            this.lblTime.Control = this.txtTime;
            this.lblTime.Location = new System.Drawing.Point(718, 72);
            this.lblTime.Name = "lblTime";
            this.lblTime.Size = new System.Drawing.Size(223, 24);
            this.lblTime.Text = "识别时间";
            this.lblTime.TextSize = new System.Drawing.Size(84, 14);
            // 
            // emptySpaceItem7
            // 
            this.emptySpaceItem7.AllowHotTrack = false;
            this.emptySpaceItem7.Location = new System.Drawing.Point(718, 0);
            this.emptySpaceItem7.Name = "emptySpaceItem7";
            this.emptySpaceItem7.Size = new System.Drawing.Size(223, 48);
            this.emptySpaceItem7.TextSize = new System.Drawing.Size(0, 0);
            // 
            // lblID
            // 
            this.lblID.Control = this.txtID;
            this.lblID.Location = new System.Drawing.Point(0, 0);
            this.lblID.Name = "lblID";
            this.lblID.Size = new System.Drawing.Size(369, 24);
            this.lblID.Text = "ID";
            this.lblID.TextSize = new System.Drawing.Size(84, 14);
            // 
            // lblQDLX
            // 
            this.lblQDLX.Control = this.txtQDLX;
            this.lblQDLX.Location = new System.Drawing.Point(0, 24);
            this.lblQDLX.Name = "lblQDLX";
            this.lblQDLX.Size = new System.Drawing.Size(369, 24);
            this.lblQDLX.Text = "清单类型";
            this.lblQDLX.TextSize = new System.Drawing.Size(84, 14);
            // 
            // lblCZR
            // 
            this.lblCZR.Control = this.txtCZR;
            this.lblCZR.Location = new System.Drawing.Point(0, 72);
            this.lblCZR.Name = "lblCZR";
            this.lblCZR.Size = new System.Drawing.Size(369, 24);
            this.lblCZR.Text = "操作人";
            this.lblCZR.TextSize = new System.Drawing.Size(84, 14);
            // 
            // lblCZSJ
            // 
            this.lblCZSJ.Control = this.txtCZSJ;
            this.lblCZSJ.Location = new System.Drawing.Point(369, 72);
            this.lblCZSJ.Name = "lblCZSJ";
            this.lblCZSJ.Size = new System.Drawing.Size(349, 24);
            this.lblCZSJ.Text = "操作时间";
            this.lblCZSJ.TextSize = new System.Drawing.Size(84, 14);
            // 
            // lblZSHKSJ
            // 
            this.lblZSHKSJ.Control = this.txtZSHKSJ;
            this.lblZSHKSJ.Location = new System.Drawing.Point(369, 48);
            this.lblZSHKSJ.Name = "lblZSHKSJ";
            this.lblZSHKSJ.Size = new System.Drawing.Size(252, 24);
            this.lblZSHKSJ.Text = "正式核扣时间";
            this.lblZSHKSJ.TextSize = new System.Drawing.Size(84, 14);
            // 
            // emptySpaceItem8
            // 
            this.emptySpaceItem8.AllowHotTrack = false;
            this.emptySpaceItem8.Location = new System.Drawing.Point(621, 48);
            this.emptySpaceItem8.Name = "emptySpaceItem8";
            this.emptySpaceItem8.Size = new System.Drawing.Size(97, 24);
            this.emptySpaceItem8.TextSize = new System.Drawing.Size(0, 0);
            // 
            // lblHL
            // 
            this.lblHL.Control = this.txtHL;
            this.lblHL.Location = new System.Drawing.Point(718, 48);
            this.lblHL.Name = "lblHL";
            this.lblHL.Size = new System.Drawing.Size(223, 24);
            this.lblHL.Text = "汇率";
            this.lblHL.TextSize = new System.Drawing.Size(84, 14);
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gd;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 96);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(1352, 533);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // layoutControlItem4
            // 
            this.layoutControlItem4.Control = this.lblRemark;
            this.layoutControlItem4.Location = new System.Drawing.Point(941, 48);
            this.layoutControlItem4.Name = "layoutControlItem4";
            this.layoutControlItem4.Size = new System.Drawing.Size(304, 24);
            this.layoutControlItem4.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem4.TextVisible = false;
            // 
            // layoutControlItem3
            // 
            this.layoutControlItem3.Location = new System.Drawing.Point(288, 0);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(896, 582);
            this.layoutControlItem3.Text = "清单类型";
            this.layoutControlItem3.TextSize = new System.Drawing.Size(72, 14);
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.Location = new System.Drawing.Point(0, 124);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(1184, 482);
            this.layoutControlItem2.Text = "操作人";
            this.layoutControlItem2.TextSize = new System.Drawing.Size(72, 14);
            // 
            // layoutControlItem5
            // 
            this.layoutControlItem5.Location = new System.Drawing.Point(288, 124);
            this.layoutControlItem5.Name = "layoutControlItem5";
            this.layoutControlItem5.Size = new System.Drawing.Size(896, 482);
            this.layoutControlItem5.Text = "操作时间";
            this.layoutControlItem5.TextSize = new System.Drawing.Size(72, 14);
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(592, 0);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(592, 606);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 24);
            this.splitContainerControl1.Name = "splitContainerControl1";
            // 
            // splitContainerControl1.Panel1
            // 
            this.splitContainerControl1.Panel1.Controls.Add(this.layoutControl1);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            // 
            // splitContainerControl1.Panel2
            // 
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.PanelVisibility = DevExpress.XtraEditors.SplitPanelVisibility.Panel1;
            this.splitContainerControl1.Size = new System.Drawing.Size(1372, 649);
            this.splitContainerControl1.SplitterPosition = 1243;
            this.splitContainerControl1.TabIndex = 9;
            // 
            // lblZT
            // 
            this.lblZT.Appearance.BorderColor = System.Drawing.Color.Red;
            this.lblZT.Appearance.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Bold);
            this.lblZT.Appearance.ForeColor = System.Drawing.Color.Red;
            this.lblZT.Appearance.Options.UseBorderColor = true;
            this.lblZT.Appearance.Options.UseFont = true;
            this.lblZT.Appearance.Options.UseForeColor = true;
            this.lblZT.Appearance.Options.UseTextOptions = true;
            this.lblZT.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblZT.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblZT.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.lblZT.Location = new System.Drawing.Point(948, 3);
            this.lblZT.Name = "lblZT";
            this.lblZT.Size = new System.Drawing.Size(107, 53);
            this.lblZT.TabIndex = 569;
            this.lblZT.Text = "未核销";
            this.lblZT.Visible = false;
            // 
            // frmPDFCK
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1372, 673);
            this.Controls.Add(this.lblZT);
            this.Controls.Add(this.splitContainerControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            referenceControl1.BindContext = "Text";
            referenceControl1.Control = this.txtHZQDBH;
            referenceControl1.DisplayMember = "核注清单编号";
            referenceControl2.BindContext = "Text";
            referenceControl2.Control = this.txtQDLX;
            referenceControl2.DisplayMember = "清单类型";
            referenceControl3.BindContext = "Text";
            referenceControl3.Control = this.txtBGDBH;
            referenceControl3.DisplayMember = "报关单编号";
            referenceControl4.BindContext = "Text";
            referenceControl4.Control = this.txtZSHKSJ;
            referenceControl4.DisplayMember = "正式核扣时间";
            referenceControl5.BindContext = "Text";
            referenceControl5.Control = this.txtGLBGDBH;
            referenceControl5.DisplayMember = "关联报关单编号";
            referenceControl6.BindContext = "Text";
            referenceControl6.Control = this.txtCZR;
            referenceControl6.DisplayMember = "操作人";
            referenceControl7.BindContext = "Text";
            referenceControl7.Control = this.txtCZSJ;
            referenceControl7.DisplayMember = "操作时间";
            referenceControl8.BindContext = "Text";
            referenceControl8.Control = this.txtID;
            referenceControl8.DisplayMember = "ID";
            referenceControl9.BindContext = "Text";
            referenceControl9.Control = this.txtHL;
            referenceControl9.DisplayMember = "汇率";
            referenceControl10.BindContext = "Text";
            referenceControl10.Control = this.lblRemark;
            referenceControl10.DisplayMember = "备注";
            this.DataBindControls.Add(referenceControl1);
            this.DataBindControls.Add(referenceControl2);
            this.DataBindControls.Add(referenceControl3);
            this.DataBindControls.Add(referenceControl4);
            this.DataBindControls.Add(referenceControl5);
            this.DataBindControls.Add(referenceControl6);
            this.DataBindControls.Add(referenceControl7);
            this.DataBindControls.Add(referenceControl8);
            this.DataBindControls.Add(referenceControl9);
            this.DataBindControls.Add(referenceControl10);
            this.Name = "frmPDFCK";
            this.Text = "出口清单";
            this.Load += new System.EventHandler(this.frmPDFCK_Load);
            ((System.ComponentModel.ISupportInitialize)(this.bs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtHZQDBH.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bar)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gd)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtHL.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtID.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTime.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGLBGDBH.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCZSJ.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCZR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBGDBH.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtQDLX.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtZSHKSJ.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtZSHKSJ.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblHZQDBH)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblBGDBH)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblGLBGDBH)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblQDLX)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblCZR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblCZSJ)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblZSHKSJ)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblHL)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1.Panel1)).EndInit();
            this.splitContainerControl1.Panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1.Panel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager bar;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem btnRefresh;
        private DevExpress.XtraBars.BarButtonItem btnSelectFile;
        private DevExpress.XtraBars.BarButtonItem btnClear;
        private DevExpress.XtraBars.BarButtonItem btnReturn;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem btnAdd;
        private DevExpress.XtraBars.BarButtonItem btnJS;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem3;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem5;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;
        private DevExpress.XtraLayout.LayoutControl layoutControl1;
        private DevExpress.XtraLayout.LayoutControlGroup Root;
        private DevExpress.XtraGrid.GridControl gd;
        private DevExpress.XtraGrid.Views.Grid.GridView gdv;
        private DevExpress.XtraGrid.Columns.GridColumn 商品序号;
        private DevExpress.XtraGrid.Columns.GridColumn 备案序号;
        private DevExpress.XtraGrid.Columns.GridColumn 商品料号;
        private DevExpress.XtraGrid.Columns.GridColumn 商品编码;
        private DevExpress.XtraGrid.Columns.GridColumn 商品名称;
        private DevExpress.XtraGrid.Columns.GridColumn 申报计量单位;
        private DevExpress.XtraGrid.Columns.GridColumn 申报数量;
        private DevExpress.XtraGrid.Columns.GridColumn 单价;
        private DevExpress.XtraGrid.Columns.GridColumn 总价;
        private DevExpress.XtraGrid.Columns.GridColumn 币制;
        private DevExpress.XtraGrid.Columns.GridColumn 法定计量单位;
        private DevExpress.XtraGrid.Columns.GridColumn 法定数量;
        private DevExpress.XtraGrid.Columns.GridColumn 第二法定计量单位;
        private DevExpress.XtraGrid.Columns.GridColumn 第二法定数量;
        private DevExpress.XtraGrid.Columns.GridColumn 原产国;
        private DevExpress.XtraEditors.TextEdit txtCZSJ;
        private DevExpress.XtraEditors.TextEdit txtCZR;
        private DevExpress.XtraEditors.TextEdit txtBGDBH;
        private DevExpress.XtraEditors.TextEdit txtQDLX;
        private DevExpress.XtraEditors.TextEdit txtHZQDBH;
        private DevExpress.XtraLayout.LayoutControlItem lblHZQDBH;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem3;
        private DevExpress.XtraLayout.LayoutControlItem lblQDLX;
        private DevExpress.XtraLayout.LayoutControlItem lblZSHKSJ;
        private DevExpress.XtraLayout.LayoutControlItem lblBGDBH;
        private DevExpress.XtraLayout.LayoutControlItem lblCZR;
        private DevExpress.XtraLayout.LayoutControlItem lblCZSJ;
        private DevExpress.XtraEditors.TextEdit txtGLBGDBH;
        private DevExpress.XtraLayout.LayoutControlItem lblGLBGDBH;
        private DevExpress.XtraBars.BarButtonItem btnHD;
        private DevExpress.XtraBars.BarButtonItem btnDel;
        private DevExpress.XtraEditors.LabelControl lblZT;
        private DevExpress.XtraEditors.TextEdit txtTime;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem4;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem6;
        private DevExpress.XtraLayout.LayoutControlItem lblTime;
        private System.Windows.Forms.Timer timer;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem7;
        private DevExpress.XtraEditors.TextEdit txtID;
        private DevExpress.XtraLayout.LayoutControlItem lblID;
        private DevExpress.XtraGrid.Columns.GridColumn 进口核注ID;
        private DevExpress.XtraEditors.DateEdit txtZSHKSJ;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem8;
        private DevExpress.XtraBars.BarCheckItem ckbEdit;
        private DevExpress.XtraEditors.TextEdit txtHL;
        private DevExpress.XtraLayout.LayoutControlItem lblHL;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;
        private System.Windows.Forms.Label lblRemark;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem4;
    }
}