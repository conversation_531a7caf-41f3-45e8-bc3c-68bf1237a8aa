﻿using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using HIH.Framework.Common.Data;
using System;
using System.Collections;
using System.Data;
using System.Windows.Forms;

namespace HIH.CMS.Main
{
    public partial class frmCKDetail : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        private string baxh = "";
        string strSql = @"select 
a.上级ID,
a.商品序号,
a.备案序号,
a.商品料号,
a.商品编码,
a.商品名称,
a.单价,
a.总价,
a.申报数量,
a.法定数量,
a.第二法定数量,
b.操作人,
b.操作时间,
a.进口核注ID
 from 出口核注清单明细表 a inner join 出口核注清单表 b ON a.上级ID = b.ID where 1 = 1 ";
        string strWhere = "";
        string strOrderBy = " order by 操作时间";
        DataTable dt = new DataTable();
        public frmCKDetail()
        {
            InitializeComponent();
            isLoadPerm = false;
        }

        private void frmCKDetail_Load(object sender, EventArgs e)
        {
            if (vInParam != null)
            {
                baxh = ((ArrayList)vInParam)[0].ToString();
            }
            LoadData();
        }

        public void LoadData()
        {
            strWhere = "";
            strWhere += " and 备案序号 = '" + baxh + "'";

            string SQL = strSql + strWhere + strOrderBy;
            dt = SqlHelper.FillDataTable(SQL);
            bs.DataSource = dt;
            gd.DataSource = bs;
            gdv.Columns["上级ID"].Visible = false;
            gdv.Columns["进口核注ID"].Visible = false;
            gdv.BestFitColumns();
        }


        private void btnReturn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DoReturn();
        }

        private void btnKSCX_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void gdv_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                GridHitInfo hitInfo = gdv.CalcHitInfo(gdv.GridControl.PointToClient(Control.MousePosition));
                if (hitInfo.InRowCell)
                {
                    //获取当前选择行的数据
                    string sjID = gdv.GetRowCellValue(gdv.FocusedRowHandle, "上级ID").ToString();
                    //双击跳入明细页面
                    ICF.ISD.SubFormShowModal("HIH.CMS.Main.dll",
                                                "HIH.CMS.Main.frmPDFCK",
                                                "出口清单导入",
                                                 new ArrayList() { sjID });

                }
            }
            catch (Exception)
            {
            }
           
        }
    }
}
